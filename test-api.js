// Simple test script to verify API integration
const API_BASE_URL = 'https://ai-nav.onrender.com';

async function testAPI() {
  console.log('Testing API endpoints...\n');

  // Test 1: Basic entities endpoint
  try {
    console.log('1. Testing basic entities endpoint...');
    const response = await fetch(`${API_BASE_URL}/entities?limit=1`);
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Basic entities endpoint working');
      console.log(`Response structure:`, Object.keys(data));
      if (data.data && Array.isArray(data.data)) {
        console.log(`Found ${data.data.length} entities`);
      }
    } else {
      const text = await response.text();
      console.log('❌ Basic entities endpoint failed');
      console.log('Response:', text.substring(0, 200));
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Entity types endpoint
  try {
    console.log('2. Testing entity types endpoint...');
    const response = await fetch(`${API_BASE_URL}/entity-types`);
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Entity types endpoint working');
      console.log(`Found ${data.length} entity types`);
      if (data.length > 0) {
        console.log('First entity type:', data[0]);
      }
    } else {
      const text = await response.text();
      console.log('❌ Entity types endpoint failed');
      console.log('Response:', text.substring(0, 200));
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Flat parameters with entity type filter
  try {
    console.log('3. Testing flat parameters with entity type filter...');
    // Get entity types first to get a valid UUID
    const typesResponse = await fetch(`${API_BASE_URL}/entity-types`);
    if (typesResponse.ok) {
      const types = await typesResponse.json();
      const toolType = types.find(t => t.name === 'AI Tool' || t.slug === 'tool');
      
      if (toolType) {
        console.log(`Using entity type: ${toolType.name} (${toolType.id})`);
        
        const response = await fetch(`${API_BASE_URL}/entities?entityTypeIds=${toolType.id}&limit=1`);
        console.log(`Status: ${response.status}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Entity type filtering working');
          console.log(`Found ${data.data ? data.data.length : 0} entities`);
        } else {
          const text = await response.text();
          console.log('❌ Entity type filtering failed');
          console.log('Response:', text.substring(0, 200));
        }
      } else {
        console.log('❌ Could not find AI Tool entity type');
      }
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 4: Flat parameters with boolean filter
  try {
    console.log('4. Testing flat parameters with boolean filter...');
    const response = await fetch(`${API_BASE_URL}/entities?has_api=true&limit=1`);
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Boolean filtering working');
      console.log(`Found ${data.data ? data.data.length : 0} entities with API`);
    } else {
      const text = await response.text();
      console.log('❌ Boolean filtering failed');
      console.log('Response:', text.substring(0, 200));
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 5: Flat parameters with array filter
  try {
    console.log('5. Testing flat parameters with array filter...');
    const response = await fetch(`${API_BASE_URL}/entities?technical_levels=BEGINNER&limit=1`);
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Array filtering working');
      console.log(`Found ${data.data ? data.data.length : 0} beginner-level entities`);
    } else {
      const text = await response.text();
      console.log('❌ Array filtering failed');
      console.log('Response:', text.substring(0, 200));
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }

  console.log('\n🏁 API testing complete!');
}

testAPI().catch(console.error);
