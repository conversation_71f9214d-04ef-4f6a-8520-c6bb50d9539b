import React, { useState, useCallback, useMemo } from 'react';
import { useDebounce } from './useDebounce';

export interface FilterState {
  // Basic filters
  searchTerm: string;
  entityTypeIds: string[];
  categoryIds: string[];
  tagIds: string[];
  featureIds: string[];
  
  // Advanced filters
  hasFreeTier?: boolean;
  apiAccess?: boolean;
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  createdAtFrom?: string;
  createdAtTo?: string;
  locationSearch?: string;
  ratingMin?: number;
  ratingMax?: number;
  reviewCountMin?: number;
  reviewCountMax?: number;
  affiliateStatus?: 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED';
  hasAffiliateLink?: boolean;
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];
  status?: string;
  submitterId?: string;

  // ✅ FLAT ENTITY-SPECIFIC FILTERS (NO MORE NESTED OBJECTS)
  // Tool/AI Tool filters
  technical_levels?: string[];
  learning_curves?: string[];
  has_api?: boolean;
  has_free_tier?: boolean;
  open_source?: boolean;
  mobile_support?: boolean;
  demo_available?: boolean;
  has_live_chat?: boolean;
  frameworks?: string[];
  libraries?: string[];
  deployment_options?: string[];
  support_channels?: string[];
  key_features_search?: string;
  use_cases_search?: string;
  target_audience_search?: string;
  customization_level?: string;
  pricing_details_search?: string;

  // Course filters
  skill_levels?: string[];
  certificate_available?: boolean;
  instructor_name?: string;
  duration_text?: string;
  enrollment_min?: number;
  enrollment_max?: number;
  prerequisites?: string;
  has_syllabus?: boolean;

  // Job filters
  employment_types?: string[];
  experience_levels?: string[];
  location_types?: string[];
  company_name?: string;
  job_title?: string;
  salary_min?: number;
  salary_max?: number;
  has_application_url?: boolean;

  // Hardware filters
  price_range?: string;
  price_min?: number;
  price_max?: number;
  memory_search?: string;
  processor_search?: string;

  // Event filters
  event_types?: string[];
  start_date_from?: string;
  start_date_to?: string;
  end_date_from?: string;
  end_date_to?: string;
  is_online?: boolean;
  location?: string;
  price_text?: string;
  has_registration_url?: boolean;
  speakers_search?: string;

  // Agency filters
  services_offered?: string[];
  industry_focus?: string[];
  has_portfolio?: boolean;

  // Software filters
  license_types?: string[];
  current_version?: string;

  // Book filters
  author_name?: string;
  isbn?: string;
  formats?: string[];
  
  // Sorting and pagination
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  page: number;
}

export interface FilterActions {
  updateFilter: (key: keyof FilterState, value: any) => void;
  updateMultipleFilters: (updates: Partial<FilterState>) => void;
  clearAllFilters: () => void;
  clearAdvancedFilters: () => void;
  resetPagination: () => void;
}

export interface UseFilterStateOptions {
  debounceDelay?: number;
  onFilterChange?: (filters: FilterState) => void;
  initialState?: Partial<FilterState>;
}

const DEFAULT_FILTER_STATE: FilterState = {
  searchTerm: '',
  entityTypeIds: [],
  categoryIds: [],
  tagIds: [],
  featureIds: [],
  sortBy: 'createdAt',
  sortOrder: 'desc',
  page: 1,
};

export function useFilterState(options: UseFilterStateOptions = {}) {
  const {
    debounceDelay = 300,
    onFilterChange,
    initialState = {},
  } = options;

  const [filters, setFilters] = useState<FilterState>({
    ...DEFAULT_FILTER_STATE,
    ...initialState,
  });

  // Debounce the filter state to avoid excessive API calls
  const debouncedFilters = useDebounce(filters, debounceDelay);

  // Calculate active filter counts for UI feedback
  const activeFilterCounts = useMemo(() => {
    const counts = {
      basic: 0,
      advanced: 0,
      entitySpecific: 0,
      total: 0,
    };

    // Basic filters
    if (filters.searchTerm) counts.basic++;
    counts.basic += filters.entityTypeIds.length;
    counts.basic += filters.categoryIds.length;
    counts.basic += filters.tagIds.length;
    counts.basic += filters.featureIds.length;

    // Advanced filters
    const advancedFields = [
      'hasFreeTier', 'apiAccess', 'locationSearch', 'ratingMin', 'ratingMax',
      'reviewCountMin', 'reviewCountMax', 'affiliateStatus', 'hasAffiliateLink',
      'createdAtFrom', 'createdAtTo', 'status', 'submitterId'
    ];
    
    advancedFields.forEach(field => {
      const value = filters[field as keyof FilterState];
      if (value !== undefined && value !== null && value !== '') {
        counts.advanced++;
      }
    });

    // Array filters
    const arrayFields = [
      'employeeCountRanges', 'fundingStages', 'pricingModels', 'priceRanges',
      'integrations', 'platforms', 'targetAudience'
    ];
    
    arrayFields.forEach(field => {
      const value = filters[field as keyof FilterState] as string[] | undefined;
      if (value && value.length > 0) {
        counts.advanced++;
      }
    });

    // Entity-specific filters (flat parameters)
    const entitySpecificFields = [
      // Tool filters
      'technical_levels', 'learning_curves', 'has_api', 'has_free_tier', 'open_source',
      'mobile_support', 'demo_available', 'has_live_chat', 'frameworks', 'libraries',
      'deployment_options', 'support_channels', 'key_features_search', 'use_cases_search',
      'target_audience_search', 'customization_level', 'pricing_details_search',

      // Course filters
      'skill_levels', 'certificate_available', 'instructor_name', 'duration_text',
      'enrollment_min', 'enrollment_max', 'prerequisites', 'has_syllabus',

      // Job filters
      'employment_types', 'experience_levels', 'location_types', 'company_name',
      'job_title', 'salary_min', 'salary_max', 'has_application_url',

      // Hardware filters
      'price_range', 'price_min', 'price_max', 'memory_search', 'processor_search',

      // Event filters
      'event_types', 'start_date_from', 'start_date_to', 'end_date_from', 'end_date_to',
      'is_online', 'location', 'price_text', 'has_registration_url', 'speakers_search',

      // Agency filters
      'services_offered', 'industry_focus', 'has_portfolio',

      // Software filters
      'license_types', 'current_version',

      // Book filters
      'author_name', 'isbn', 'formats'
    ];

    entitySpecificFields.forEach(field => {
      const value = filters[field as keyof FilterState];
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          counts.entitySpecific++;
        } else if (!Array.isArray(value)) {
          counts.entitySpecific++;
        }
      }
    });

    counts.total = counts.basic + counts.advanced + counts.entitySpecific;

    return counts;
  }, [filters]);

  const updateFilter = useCallback((key: keyof FilterState, value: any) => {
    setFilters(prev => {
      const newFilters = { ...prev, [key]: value };
      
      // Reset pagination when filters change (except for page itself)
      if (key !== 'page' && key !== 'sortBy' && key !== 'sortOrder') {
        newFilters.page = 1;
      }
      
      return newFilters;
    });
  }, []);

  const updateMultipleFilters = useCallback((updates: Partial<FilterState>) => {
    setFilters(prev => {
      const newFilters = { ...prev, ...updates };
      
      // Reset pagination if any non-pagination filters changed
      const hasNonPaginationChanges = Object.keys(updates).some(
        key => key !== 'page' && key !== 'sortBy' && key !== 'sortOrder'
      );
      
      if (hasNonPaginationChanges && !updates.page) {
        newFilters.page = 1;
      }
      
      return newFilters;
    });
  }, []);

  const clearAllFilters = useCallback(() => {
    setFilters(DEFAULT_FILTER_STATE);
  }, []);

  const clearAdvancedFilters = useCallback(() => {
    setFilters(prev => ({
      ...prev,
      hasFreeTier: undefined,
      apiAccess: undefined,
      employeeCountRanges: undefined,
      fundingStages: undefined,
      pricingModels: undefined,
      priceRanges: undefined,
      createdAtFrom: undefined,
      createdAtTo: undefined,
      locationSearch: undefined,
      ratingMin: undefined,
      ratingMax: undefined,
      reviewCountMin: undefined,
      reviewCountMax: undefined,
      affiliateStatus: undefined,
      hasAffiliateLink: undefined,
      integrations: undefined,
      platforms: undefined,
      targetAudience: undefined,
      status: undefined,
      submitterId: undefined,

      // Clear all entity-specific flat parameters
      technical_levels: undefined,
      learning_curves: undefined,
      has_api: undefined,
      has_free_tier: undefined,
      open_source: undefined,
      mobile_support: undefined,
      demo_available: undefined,
      has_live_chat: undefined,
      frameworks: undefined,
      libraries: undefined,
      deployment_options: undefined,
      support_channels: undefined,
      key_features_search: undefined,
      use_cases_search: undefined,
      target_audience_search: undefined,
      customization_level: undefined,
      pricing_details_search: undefined,
      skill_levels: undefined,
      certificate_available: undefined,
      instructor_name: undefined,
      duration_text: undefined,
      enrollment_min: undefined,
      enrollment_max: undefined,
      prerequisites: undefined,
      has_syllabus: undefined,
      employment_types: undefined,
      experience_levels: undefined,
      location_types: undefined,
      company_name: undefined,
      job_title: undefined,
      salary_min: undefined,
      salary_max: undefined,
      has_application_url: undefined,
      price_range: undefined,
      price_min: undefined,
      price_max: undefined,
      memory_search: undefined,
      processor_search: undefined,
      event_types: undefined,
      start_date_from: undefined,
      start_date_to: undefined,
      end_date_from: undefined,
      end_date_to: undefined,
      is_online: undefined,
      location: undefined,
      price_text: undefined,
      has_registration_url: undefined,
      speakers_search: undefined,
      services_offered: undefined,
      industry_focus: undefined,
      has_portfolio: undefined,
      license_types: undefined,
      current_version: undefined,
      author_name: undefined,
      isbn: undefined,
      formats: undefined,

      page: 1,
    }));
  }, []);

  const resetPagination = useCallback(() => {
    updateFilter('page', 1);
  }, [updateFilter]);

  // Trigger callback when debounced filters change
  React.useEffect(() => {
    if (onFilterChange) {
      onFilterChange(debouncedFilters);
    }
  }, [debouncedFilters, onFilterChange]);

  const actions: FilterActions = {
    updateFilter,
    updateMultipleFilters,
    clearAllFilters,
    clearAdvancedFilters,
    resetPagination,
  };

  return {
    filters,
    debouncedFilters,
    activeFilterCounts,
    actions,
  };
}
