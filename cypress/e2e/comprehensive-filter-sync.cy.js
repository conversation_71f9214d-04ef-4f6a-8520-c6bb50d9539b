describe('Comprehensive Filter UI Synchronization', () => {
  beforeEach(() => {
    cy.visit('/browse')
    cy.wait(2000) // Wait for initial load
  })

  it('should show correct filter states when navigating to URL with multiple filter types', () => {
    // Navigate to URL with various filter types
    const testUrl = '/browse?entityTypeIds=fd181400-c9e6-431c-a8bd-c068d0491aba&pricingModels=FREE,PAID&hasFreeTier=true&technical_levels=BEGINNER,INTERMEDIATE&has_api=true'
    
    cy.visit(testUrl)
    cy.wait(3000) // Wait for filters to load and render
    
    // Verify URL contains expected parameters
    cy.url().should('include', 'pricingModels=FREE,PAID')
    cy.url().should('include', 'hasFreeTier=true')
    cy.url().should('include', 'technical_levels=BEGINNER,INTERMEDIATE')
    cy.url().should('include', 'has_api=true')
    
    // Check that filters are visible (we'll look for any checkbox inputs)
    cy.get('input[type="checkbox"]').should('have.length.at.least', 5)
    
    // Check that some checkboxes are checked (indicating filters are applied)
    cy.get('input[type="checkbox"]:checked').should('have.length.at.least', 3)
    
    // Verify that the page shows filtered results
    cy.get('[data-testid="browse-page"]').should('exist')
  })

  it('should maintain filter state when clicking filters and updating URL', () => {
    // Start with AI Tool selected to show entity-specific filters
    cy.visit('/browse?entityTypeIds=fd181400-c9e6-431c-a8bd-c068d0491aba')
    cy.wait(3000)
    
    // Look for any available checkbox and click it
    cy.get('input[type="checkbox"]:not(:checked)').first().then(($checkbox) => {
      if ($checkbox.length > 0) {
        cy.wrap($checkbox).click({ force: true })
        
        // Wait for URL to update
        cy.wait(1000)
        
        // Verify the checkbox is now checked
        cy.wrap($checkbox).should('be.checked')
        
        // Verify URL was updated (should contain some filter parameter)
        cy.url().should('match', /[?&]\w+=(true|[A-Z,]+)/)
      }
    })
  })

  it('should handle page refresh correctly with filters applied', () => {
    // Navigate to URL with filters
    const testUrl = '/browse?entityTypeIds=fd181400-c9e6-431c-a8bd-c068d0491aba&hasFreeTier=true'
    
    cy.visit(testUrl)
    cy.wait(3000)
    
    // Count checked checkboxes before refresh
    cy.get('input[type="checkbox"]:checked').then(($checkedBefore) => {
      const checkedCountBefore = $checkedBefore.length
      
      // Refresh the page
      cy.reload()
      cy.wait(3000)
      
      // Verify URL still contains filters
      cy.url().should('include', 'hasFreeTier=true')
      
      // Verify similar number of checkboxes are still checked
      cy.get('input[type="checkbox"]:checked').should('have.length.at.least', Math.max(1, checkedCountBefore - 1))
    })
  })

  it('should clear filters correctly', () => {
    // Start with filters applied
    cy.visit('/browse?entityTypeIds=fd181400-c9e6-431c-a8bd-c068d0491aba&hasFreeTier=true&technical_levels=BEGINNER')
    cy.wait(3000)
    
    // Look for clear filters button and click it
    cy.get('body').then(($body) => {
      // Try different possible selectors for clear button
      const clearSelectors = [
        '[data-testid="clear-all-filters"]',
        'button:contains("Clear All")',
        'button:contains("Clear")',
        'button:contains("Reset")'
      ]
      
      let buttonFound = false
      for (const selector of clearSelectors) {
        if ($body.find(selector).length > 0) {
          cy.get(selector).first().click()
          buttonFound = true
          break
        }
      }
      
      if (buttonFound) {
        cy.wait(1000)
        
        // Verify URL is cleared of most filters
        cy.url().should('not.include', 'hasFreeTier=true')
        cy.url().should('not.include', 'technical_levels=BEGINNER')
      }
    })
  })

  it('should handle array filters correctly', () => {
    // Test with multiple values in array filters
    cy.visit('/browse?entityTypeIds=fd181400-c9e6-431c-a8bd-c068d0491aba&technical_levels=BEGINNER,INTERMEDIATE,ADVANCED')
    cy.wait(3000)
    
    // Verify URL contains the array parameter
    cy.url().should('include', 'technical_levels=BEGINNER,INTERMEDIATE,ADVANCED')
    
    // Should have multiple checkboxes checked
    cy.get('input[type="checkbox"]:checked').should('have.length.at.least', 2)
    
    // Verify page loads without errors
    cy.get('[data-testid="browse-page"]').should('exist')
  })
})
